using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Services;
using PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Builders;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Services;

public class RestoreSchedulesCommandHandlerTests
{
    private readonly Mock<ILogger<RestoreSchedulesCommandHandler>> _loggerMock;
    private readonly Mock<IApifyTaskService> _taskServiceMock;
    private readonly Mock<IApifyScheduleService> _scheduleServiceMock;
    private readonly Mock<IApifyWebhookService> _webhookServiceMock;
    private readonly Mock<IScrapingConfigurationService> _configurationServiceMock;
    private readonly RestoreSchedulesCommandHandler _sut;

    public RestoreSchedulesCommandHandlerTests()
    {
        _loggerMock = new Mock<ILogger<RestoreSchedulesCommandHandler>>();
        _taskServiceMock = new Mock<IApifyTaskService>();
        _scheduleServiceMock = new Mock<IApifyScheduleService>();
        _webhookServiceMock = new Mock<IApifyWebhookService>();
        _configurationServiceMock = new Mock<IScrapingConfigurationService>();

        _sut = new RestoreSchedulesCommandHandler(
            _loggerMock.Object,
            _taskServiceMock.Object,
            _scheduleServiceMock.Object,
            _webhookServiceMock.Object,
            _configurationServiceMock.Object);
    }

    [Fact]
    public async Task Consume_WhenNoEnabledJournalsWithSchedules_ReturnsEmptyResult()
    {
        // Arrange
        var command = new RestoreSchedulesCommand
        {
            Journals = new List<JournalScheduleInfo>()
        };

        _configurationServiceMock.Setup(x => x.GetWebhookUrl()).Returns(string.Empty);

        // Act
        var result = await _sut.Consume(command);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.JournalsProcessed);
        Assert.Equal(0, result.TasksCreated);
        Assert.Equal(0, result.SchedulesCreated);
        Assert.Equal(0, result.WebhooksCreated);
        Assert.Contains("Webhook URL not configured", result.Errors);
    }

    [Fact]
    public async Task Consume_WhenWebhookUrlNotConfigured_ReturnsError()
    {
        // Arrange
        var command = new RestoreSchedulesCommand
        {
            Journals = new List<JournalScheduleInfo>
            {
                new JournalScheduleInfoBuilder().WithName("Journal 1").WithCronExpression("0 9 * * 1").Build()
            }
        };

        _configurationServiceMock.Setup(x => x.GetWebhookUrl()).Returns(string.Empty);

        // Act
        var result = await _sut.Consume(command);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.JournalsProcessed);
        Assert.Equal(0, result.TasksCreated);
        Assert.Equal(0, result.SchedulesCreated);
        Assert.Equal(0, result.WebhooksCreated);
        Assert.Contains("Webhook URL not configured", result.Errors);
    }

    [Fact]
    public async Task Consume_WhenAllOperationsSucceed_ReturnsSuccessResult()
    {
        // Arrange
        var command = new RestoreSchedulesCommand
        {
            Journals = new List<JournalScheduleInfo>
            {
                new JournalScheduleInfoBuilder().WithName("Nature Medicine").WithCronExpression("0 9 * * 1").WithUrl("https://nature.com/nm").Build(),
                new JournalScheduleInfoBuilder().WithName("The Lancet").WithCronExpression("0 10 * * 2").WithUrl("https://thelancet.com").Build()
            }
        };

        _configurationServiceMock.Setup(x => x.GetWebhookUrl()).Returns("https://localhost:5001/webhook");

        _taskServiceMock.Setup(x => x.CreateGroupTaskAsync(It.IsAny<IEnumerable<JournalScheduleInfo>>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("task-123");

        _scheduleServiceMock.Setup(x => x.CreateScheduleForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _webhookServiceMock.Setup(x => x.CreateWebhookForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sut.Consume(command);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.JournalsProcessed);
        Assert.Equal(2, result.TasksCreated); 
        Assert.Equal(2, result.SchedulesCreated);
        Assert.Equal(2, result.WebhooksCreated);
        Assert.Empty(result.Errors);
        Assert.Equal(6, result.Messages.Count);

        _taskServiceMock.Verify(x => x.CreateGroupTaskAsync(It.IsAny<IEnumerable<JournalScheduleInfo>>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
        _scheduleServiceMock.Verify(x => x.CreateScheduleForTaskAsync("task-123", It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
        _webhookServiceMock.Verify(x => x.CreateWebhookForTaskAsync("task-123", "https://localhost:5001/webhook", It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public async Task Consume_WhenTaskCreationFails_ReturnsError()
    {
        // Arrange
        var command = new RestoreSchedulesCommand
        {
            Journals = new List<JournalScheduleInfo>
            {
                new JournalScheduleInfoBuilder().WithName("Nature Medicine").WithCronExpression("0 9 * * 1").WithUrl("https://nature.com/nm").Build()
            }
        };

        _configurationServiceMock.Setup(x => x.GetWebhookUrl()).Returns("https://localhost:5001/webhook");

        _taskServiceMock.Setup(x => x.CreateGroupTaskAsync(It.IsAny<IEnumerable<JournalScheduleInfo>>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(string.Empty);

        // Act
        var result = await _sut.Consume(command);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.JournalsProcessed);
        Assert.Equal(0, result.TasksCreated);
        Assert.Equal(0, result.SchedulesCreated);
        Assert.Equal(0, result.WebhooksCreated);
        Assert.Contains("Failed to create group task for cron '0 9 * * 1': Task ID was null or empty", result.Errors);

        _scheduleServiceMock.Verify(x => x.CreateScheduleForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        _webhookServiceMock.Verify(x => x.CreateWebhookForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Consume_WhenGeneralExceptionOccurs_ReturnsError()
    {
        // Arrange
        var command = new RestoreSchedulesCommand
        {
            Journals = new List<JournalScheduleInfo>
            {
                new JournalScheduleInfoBuilder().WithName("Test Journal").WithCronExpression("0 9 * * 1").Build()
            }
        };

        _configurationServiceMock.Setup(x => x.GetWebhookUrl()).Returns("https://localhost:5001/webhook");
        _taskServiceMock.Setup(x => x.CreateGroupTaskAsync(It.IsAny<IEnumerable<JournalScheduleInfo>>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act
        var result = await _sut.Consume(command);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.JournalsProcessed);
        Assert.Equal(0, result.TasksCreated);
        Assert.Equal(0, result.SchedulesCreated);
        Assert.Equal(0, result.WebhooksCreated);
        Assert.Contains("Database connection failed", result.Errors.First());
    }

    [Fact]
    public async Task Consume_GroupsJournalsByCronExpression()
    {
        // Arrange
        var command = new RestoreSchedulesCommand
        {
            Journals = new List<JournalScheduleInfo>
            {
                new JournalScheduleInfoBuilder().WithName("Journal 1").WithCronExpression("0 9 * * 1").WithUrl("https://journal1.com").Build(),
                new JournalScheduleInfoBuilder().WithName("Journal 2").WithCronExpression("0 9 * * 1").WithUrl("https://journal2.com").Build(), // Same cron
                new JournalScheduleInfoBuilder().WithName("Journal 3").WithCronExpression("0 10 * * 2").WithUrl("https://journal3.com").Build()
            }
        };

        _configurationServiceMock.Setup(x => x.GetWebhookUrl()).Returns("https://localhost:5001/webhook");

        _taskServiceMock.Setup(x => x.CreateGroupTaskAsync(It.IsAny<IEnumerable<JournalScheduleInfo>>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("task-123");

        _scheduleServiceMock.Setup(x => x.CreateScheduleForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _webhookServiceMock.Setup(x => x.CreateWebhookForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sut.Consume(command);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.JournalsProcessed);
        Assert.Equal(2, result.TasksCreated); 
        Assert.Equal(2, result.SchedulesCreated);
        Assert.Equal(2, result.WebhooksCreated);
        Assert.Empty(result.Errors);

        _taskServiceMock.Verify(x => x.CreateGroupTaskAsync(It.IsAny<IEnumerable<JournalScheduleInfo>>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
        _scheduleServiceMock.Verify(x => x.CreateScheduleForTaskAsync("task-123", It.IsAny<string>(), "0 9 * * 1", It.IsAny<CancellationToken>()), Times.Once);
        _scheduleServiceMock.Verify(x => x.CreateScheduleForTaskAsync("task-123", It.IsAny<string>(), "0 10 * * 2", It.IsAny<CancellationToken>()), Times.Once);
    }
}
