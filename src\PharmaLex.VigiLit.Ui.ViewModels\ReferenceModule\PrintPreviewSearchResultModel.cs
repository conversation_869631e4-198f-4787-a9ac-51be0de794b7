using System.Collections.Generic;
using Microsoft.Graph.Models;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class PrintPreviewSearchResultModel
{
    public int Id { get; set; }
    public string SourceId { get; set; }
    public string SourceTextLabel { get; set; }
    public string Doi { get; set; }
    public DateTime DateRevised { get; set; }

    public string Title { get; set; }
    public string Abstract { get; set; }
    public string Authors { get; set; }
    public string MeshTerms { get; set; }
    public string Keywords { get; set; }
    public string AffiliationTextFirstAuthor { get; set; }

    public string Substance { get; set; }
    public string ClassificationCategory { get; set; }
    public string MinimalCriteria { get; set; }
    public string CountryOfOccurrence { get; set; }
    public string PSURRelevanceAbstract { get; set; }
    public string PvSafetyDatabaseId { get; set; }
    public string DosageForm { get; set; }
    public string PotentialCaseAdditionalInformation { get; set; }

    public List<string> PotentialCaseAdditionalInformationList { get; set; } = new List<string>();

    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
}
